import React from 'react';

type SpacingSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';
type SpacingType = 'padding' | 'margin' | 'both';
type SpacingDirection = 'all' | 'x' | 'y' | 'top' | 'bottom' | 'left' | 'right';

interface MobileSpacingProps {
    children: React.ReactNode;
    size?: SpacingSize;
    type?: SpacingType;
    direction?: SpacingDirection;
    className?: string;
}

const MobileSpacing: React.FC<MobileSpacingProps> = ({
    children,
    size = 'md',
    type = 'padding',
    direction = 'all',
    className = ''
}) => {
    const getSpacingClasses = () => {
        const classes: string[] = [];

        if (type === 'padding' || type === 'both') {
            switch (direction) {
                case 'all':
                    classes.push(`mobile-spacing-${size}`);
                    break;
                case 'x':
                    classes.push(`mobile-spacing-x-${size}`);
                    break;
                case 'y':
                    classes.push(`mobile-spacing-y-${size}`);
                    break;
                case 'top':
                    classes.push(`pt-${getSizeValue(size)}`);
                    break;
                case 'bottom':
                    classes.push(`pb-${getSizeValue(size)}`);
                    break;
                case 'left':
                    classes.push(`pl-${getSizeValue(size)}`);
                    break;
                case 'right':
                    classes.push(`pr-${getSizeValue(size)}`);
                    break;
            }
        }

        if (type === 'margin' || type === 'both') {
            switch (direction) {
                case 'all':
                    classes.push(`mobile-margin-${size}`);
                    break;
                case 'x':
                    classes.push(`mx-${getSizeValue(size)}`);
                    break;
                case 'y':
                    classes.push(`mobile-margin-y-${size}`);
                    break;
                case 'top':
                    classes.push(`mt-${getSizeValue(size)}`);
                    break;
                case 'bottom':
                    classes.push(`mb-${getSizeValue(size)}`);
                    break;
                case 'left':
                    classes.push(`ml-${getSizeValue(size)}`);
                    break;
                case 'right':
                    classes.push(`mr-${getSizeValue(size)}`);
                    break;
            }
        }

        return classes.join(' ');
    };

    const getSizeValue = (size: SpacingSize): string => {
        switch (size) {
            case 'xs': return '2';
            case 'sm': return '3';
            case 'md': return '4';
            case 'lg': return '6';
            case 'xl': return '8';
            default: return '4';
        }
    };

    return (
        <div className={`${getSpacingClasses()} ${className}`}>
            {children}
        </div>
    );
};

interface MobileSectionProps {
    children: React.ReactNode;
    spacing?: 'normal' | 'large';
    className?: string;
}

const MobileSectionSpacing: React.FC<MobileSectionProps> = ({
    children,
    spacing = 'normal',
    className = ''
}) => {
    const spacingClass = spacing === 'large' 
        ? 'mobile-section-spacing-lg' 
        : 'mobile-section-spacing';

    return (
        <div className={`${spacingClass} ${className}`}>
            {children}
        </div>
    );
};

interface MobileContentProps {
    children: React.ReactNode;
    size?: 'sm' | 'md' | 'lg';
    className?: string;
}

const MobileContentContainer: React.FC<MobileContentProps> = ({
    children,
    size = 'md',
    className = ''
}) => {
    const getContainerClass = () => {
        switch (size) {
            case 'sm':
                return 'mobile-content-container-sm';
            case 'lg':
                return 'mobile-content-container-lg';
            default:
                return 'mobile-content-container';
        }
    };

    return (
        <div className={`${getContainerClass()} ${className}`}>
            {children}
        </div>
    );
};

interface MobileReadingContentProps {
    children: React.ReactNode;
    className?: string;
}

const MobileReadingContent: React.FC<MobileReadingContentProps> = ({
    children,
    className = ''
}) => {
    return (
        <div className={`mobile-reading-content ${className}`}>
            {children}
        </div>
    );
};

interface SpacerProps {
    size?: SpacingSize;
    direction?: 'vertical' | 'horizontal';
    className?: string;
}

const MobileSpacer: React.FC<SpacerProps> = ({
    size = 'md',
    direction = 'vertical',
    className = ''
}) => {
    const getSpacerClasses = () => {
        const sizeMap = {
            xs: direction === 'vertical' ? 'h-2' : 'w-2',
            sm: direction === 'vertical' ? 'h-3' : 'w-3',
            md: direction === 'vertical' ? 'h-4' : 'w-4',
            lg: direction === 'vertical' ? 'h-6' : 'w-6',
            xl: direction === 'vertical' ? 'h-8' : 'w-8'
        };

        return sizeMap[size];
    };

    return <div className={`${getSpacerClasses()} ${className}`} />;
};

interface ResponsiveSpacingProps {
    children: React.ReactNode;
    mobile?: SpacingSize;
    tablet?: SpacingSize;
    desktop?: SpacingSize;
    type?: SpacingType;
    direction?: SpacingDirection;
    className?: string;
}

const ResponsiveSpacing: React.FC<ResponsiveSpacingProps> = ({
    children,
    mobile = 'md',
    tablet = 'lg',
    desktop = 'xl',
    type = 'padding',
    direction = 'all',
    className = ''
}) => {
    const getResponsiveClasses = () => {
        const classes: string[] = [];
        
        // Mobile classes
        if (type === 'padding' || type === 'both') {
            classes.push(`mobile-spacing-${mobile}`);
        }
        if (type === 'margin' || type === 'both') {
            classes.push(`mobile-margin-${mobile}`);
        }

        // Tablet and desktop classes would be added here
        // This is a simplified version focusing on mobile-first approach

        return classes.join(' ');
    };

    return (
        <div className={`${getResponsiveClasses()} ${className}`}>
            {children}
        </div>
    );
};

export {
    MobileSpacing,
    MobileSectionSpacing,
    MobileContentContainer,
    MobileReadingContent,
    MobileSpacer,
    ResponsiveSpacing
};

export default MobileSpacing;
