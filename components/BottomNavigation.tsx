import React, { useContext, useState } from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { BlogContext } from '../context/SupabaseBlogContext';
import { SearchIcon, MenuIcon } from './icons';
import { HapticButton, HapticLink } from './HapticButton';
import { useHapticFeedback } from '../utils/hapticFeedback';

interface BottomNavProps {
    onSearchClick?: () => void;
    onCategoriesClick?: () => void;
}

const BottomNavigation: React.FC<BottomNavProps> = ({ onSearchClick, onCategoriesClick }) => {
    const context = useContext(BlogContext);
    const location = useLocation();
    const [showCategories, setShowCategories] = useState(false);
    const haptic = useHapticFeedback();

    const NavItem: React.FC<{ 
        to: string; 
        icon: React.ReactNode; 
        label: string; 
        onClick?: () => void;
    }> = ({ to, icon, label, onClick }) => {
        const isActive = location.pathname === to;
        
        return (
            <HapticLink
                href={to}
                hapticType="navigation"
                onClick={(e) => {
                    e.preventDefault();
                    haptic.navigation();
                    onClick?.();
                    window.location.href = to;
                }}
                className={`flex flex-col items-center justify-center py-2 px-3 transition-all duration-200 ${
                    isActive
                        ? 'text-primary'
                        : 'text-muted-foreground hover:text-foreground'
                }`}
            >
                <div className={`p-2 rounded-lg transition-all duration-200 ${
                    isActive ? 'bg-primary/10' : 'hover:bg-accent/50'
                }`}>
                    {icon}
                </div>
                <span className="text-xs font-medium mt-1">{label}</span>
            </HapticLink>
        );
    };

    const ActionButton: React.FC<{ 
        icon: React.ReactNode; 
        label: string; 
        onClick: () => void;
        isActive?: boolean;
    }> = ({ icon, label, onClick, isActive = false }) => (
        <HapticButton
            hapticType="light"
            onClick={() => {
                haptic.light();
                onClick();
            }}
            className={`flex flex-col items-center justify-center py-2 px-3 transition-all duration-200 ${
                isActive
                    ? 'text-primary'
                    : 'text-muted-foreground hover:text-foreground'
            }`}
        >
            <div className={`p-2 rounded-lg transition-all duration-200 ${
                isActive ? 'bg-primary/10' : 'hover:bg-accent/50'
            }`}>
                {icon}
            </div>
            <span className="text-xs font-medium mt-1">{label}</span>
        </HapticButton>
    );

    return (
        <>
            {/* Categories Overlay */}
            {showCategories && (
                <div 
                    className="fixed inset-0 bg-black/20 z-40 md:hidden"
                    onClick={() => setShowCategories(false)}
                >
                    <div className="absolute bottom-20 left-4 right-4 bg-background/95 backdrop-blur-md rounded-2xl border border-border/50 shadow-xl p-4 max-h-80 overflow-y-auto">
                        <h3 className="text-lg font-semibold text-foreground mb-4">Categories</h3>
                        <div className="grid grid-cols-2 gap-2">
                            {context?.categories.map((category) => (
                                <NavLink
                                    key={category.id}
                                    to={`/category/${category.slug}`}
                                    onClick={() => setShowCategories(false)}
                                    className="mobile-link text-sm text-foreground hover:text-primary transition-all duration-200 hover:bg-accent/30 active:bg-accent/50 active:scale-95"
                                >
                                    {category.name}
                                </NavLink>
                            ))}
                        </div>
                    </div>
                </div>
            )}

            {/* Bottom Navigation Bar */}
            <nav className="md:hidden fixed bottom-0 left-0 right-0 bg-background/95 backdrop-blur-md border-t border-border/50 z-30 safe-area-pb">
                <div className="flex items-center justify-around px-2 py-1">
                    <NavItem
                        to="/"
                        icon={
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                            </svg>
                        }
                        label="Home"
                    />

                    <ActionButton
                        icon={<SearchIcon className="w-5 h-5" />}
                        label="Search"
                        onClick={() => onSearchClick?.()}
                    />

                    <ActionButton
                        icon={
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                            </svg>
                        }
                        label="Categories"
                        onClick={() => setShowCategories(!showCategories)}
                        isActive={showCategories}
                    />

                    <NavItem
                        to="/all-posts"
                        icon={
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                            </svg>
                        }
                        label="Articles"
                    />

                    <NavItem
                        to="/admin"
                        icon={
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                        }
                        label="Profile"
                    />
                </div>
            </nav>
        </>
    );
};

export default BottomNavigation;
