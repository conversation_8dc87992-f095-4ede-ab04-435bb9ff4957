import React from 'react';

interface ResponsiveGridProps {
    children: React.ReactNode;
    columns?: {
        mobile?: 1 | 2;
        tablet?: 1 | 2 | 3;
        desktop?: 1 | 2 | 3 | 4 | 6 | 12;
    };
    gap?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl';
    className?: string;
    mobileLayout?: 'stack' | 'grid';
}

const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
    children,
    columns = { mobile: 1, tablet: 2, desktop: 3 },
    gap = 'md',
    className = '',
    mobileLayout = 'stack'
}) => {
    const getGapClasses = () => {
        switch (gap) {
            case 'none':
                return 'gap-0';
            case 'xs':
                return 'gap-1 md:gap-2';
            case 'sm':
                return 'gap-2 md:gap-3';
            case 'md':
                return 'gap-4 md:gap-6';
            case 'lg':
                return 'gap-6 md:gap-8';
            case 'xl':
                return 'gap-8 md:gap-12';
            default:
                return 'gap-4 md:gap-6';
        }
    };

    const getColumnClasses = () => {
        const { mobile = 1, tablet = 2, desktop = 3 } = columns;
        
        if (mobileLayout === 'stack') {
            return `md:grid md:grid-cols-${tablet} lg:grid-cols-${desktop}`;
        }
        
        return `grid grid-cols-${mobile} md:grid-cols-${tablet} lg:grid-cols-${desktop}`;
    };

    const getMobileLayoutClasses = () => {
        if (mobileLayout === 'stack') {
            return 'space-y-4 md:space-y-0';
        }
        return '';
    };

    return (
        <div className={`${getColumnClasses()} ${getGapClasses()} ${getMobileLayoutClasses()} ${className}`}>
            {children}
        </div>
    );
};

interface MasonryGridProps {
    children: React.ReactNode;
    columns?: {
        mobile?: 1 | 2;
        tablet?: 2 | 3;
        desktop?: 3 | 4;
    };
    gap?: 'sm' | 'md' | 'lg';
    className?: string;
}

const MasonryGrid: React.FC<MasonryGridProps> = ({
    children,
    columns = { mobile: 1, tablet: 2, desktop: 3 },
    gap = 'md',
    className = ''
}) => {
    const getGapClasses = () => {
        switch (gap) {
            case 'sm':
                return 'gap-2 md:gap-4';
            case 'md':
                return 'gap-4 md:gap-6';
            case 'lg':
                return 'gap-6 md:gap-8';
            default:
                return 'gap-4 md:gap-6';
        }
    };

    const { mobile = 1, tablet = 2, desktop = 3 } = columns;

    return (
        <div 
            className={`columns-${mobile} md:columns-${tablet} lg:columns-${desktop} ${getGapClasses()} ${className}`}
            style={{ columnFill: 'balance' }}
        >
            {React.Children.map(children, (child, index) => (
                <div key={index} className="break-inside-avoid mb-4 md:mb-6">
                    {child}
                </div>
            ))}
        </div>
    );
};

interface FlexGridProps {
    children: React.ReactNode;
    minItemWidth?: string;
    gap?: 'sm' | 'md' | 'lg';
    className?: string;
    mobileStack?: boolean;
}

const FlexGrid: React.FC<FlexGridProps> = ({
    children,
    minItemWidth = '300px',
    gap = 'md',
    className = '',
    mobileStack = true
}) => {
    const getGapClasses = () => {
        switch (gap) {
            case 'sm':
                return 'gap-2 md:gap-4';
            case 'md':
                return 'gap-4 md:gap-6';
            case 'lg':
                return 'gap-6 md:gap-8';
            default:
                return 'gap-4 md:gap-6';
        }
    };

    const baseClasses = mobileStack 
        ? `flex flex-col md:grid ${getGapClasses()}`
        : `grid ${getGapClasses()}`;

    const gridTemplateColumns = mobileStack 
        ? { gridTemplateColumns: `repeat(auto-fit, minmax(${minItemWidth}, 1fr))` }
        : { gridTemplateColumns: `repeat(auto-fit, minmax(min(${minItemWidth}, 100%), 1fr))` };

    return (
        <div 
            className={`${baseClasses} ${className}`}
            style={!mobileStack || window.innerWidth >= 768 ? gridTemplateColumns : undefined}
        >
            {children}
        </div>
    );
};

interface AutoGridProps {
    children: React.ReactNode;
    minItemWidth?: string;
    maxColumns?: number;
    gap?: 'sm' | 'md' | 'lg';
    className?: string;
}

const AutoGrid: React.FC<AutoGridProps> = ({
    children,
    minItemWidth = '250px',
    maxColumns = 4,
    gap = 'md',
    className = ''
}) => {
    const getGapClasses = () => {
        switch (gap) {
            case 'sm':
                return 'gap-2 md:gap-4';
            case 'md':
                return 'gap-4 md:gap-6';
            case 'lg':
                return 'gap-6 md:gap-8';
            default:
                return 'gap-4 md:gap-6';
        }
    };

    return (
        <div 
            className={`grid ${getGapClasses()} ${className}`}
            style={{
                gridTemplateColumns: `repeat(auto-fit, minmax(min(${minItemWidth}, 100%), 1fr))`,
                gridTemplateRows: 'masonry' // Future CSS feature
            }}
        >
            {children}
        </div>
    );
};

interface GridItemProps {
    children: React.ReactNode;
    span?: {
        mobile?: 1 | 2;
        tablet?: 1 | 2 | 3;
        desktop?: 1 | 2 | 3 | 4 | 6 | 12;
    };
    className?: string;
}

const GridItem: React.FC<GridItemProps> = ({
    children,
    span = { mobile: 1, tablet: 1, desktop: 1 },
    className = ''
}) => {
    const getSpanClasses = () => {
        const { mobile = 1, tablet = 1, desktop = 1 } = span;
        return `col-span-${mobile} md:col-span-${tablet} lg:col-span-${desktop}`;
    };

    return (
        <div className={`${getSpanClasses()} ${className}`}>
            {children}
        </div>
    );
};

export { ResponsiveGrid, MasonryGrid, FlexGrid, AutoGrid, GridItem };
export default ResponsiveGrid;
