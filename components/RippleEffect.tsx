import React, { useState, useRef, useCallback } from 'react';

interface RippleProps {
    x: number;
    y: number;
    size: number;
    id: string;
}

interface RippleEffectProps {
    children: React.ReactNode;
    className?: string;
    rippleColor?: string;
    rippleDuration?: number;
    rippleOpacity?: number;
    disabled?: boolean;
    onClick?: (e: React.MouseEvent) => void;
    onMouseDown?: (e: React.MouseEvent) => void;
    onTouchStart?: (e: React.TouchEvent) => void;
}

const RippleEffect: React.FC<RippleEffectProps> = ({
    children,
    className = '',
    rippleColor = 'rgba(255, 255, 255, 0.6)',
    rippleDuration = 600,
    rippleOpacity = 0.6,
    disabled = false,
    onClick,
    onMouseDown,
    onTouchStart,
    ...props
}) => {
    const [ripples, setRipples] = useState<RippleProps[]>([]);
    const containerRef = useRef<HTMLDivElement>(null);

    const createRipple = useCallback((event: React.MouseEvent | React.TouchEvent) => {
        if (disabled || !containerRef.current) return;

        const rect = containerRef.current.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        
        let x: number, y: number;
        
        if ('touches' in event) {
            // Touch event
            const touch = event.touches[0];
            x = touch.clientX - rect.left - size / 2;
            y = touch.clientY - rect.top - size / 2;
        } else {
            // Mouse event
            x = event.clientX - rect.left - size / 2;
            y = event.clientY - rect.top - size / 2;
        }

        const newRipple: RippleProps = {
            x,
            y,
            size,
            id: Date.now().toString()
        };

        setRipples(prev => [...prev, newRipple]);

        // Remove ripple after animation completes
        setTimeout(() => {
            setRipples(prev => prev.filter(ripple => ripple.id !== newRipple.id));
        }, rippleDuration);
    }, [disabled, rippleDuration]);

    const handleMouseDown = (e: React.MouseEvent) => {
        createRipple(e);
        onMouseDown?.(e);
    };

    const handleTouchStart = (e: React.TouchEvent) => {
        createRipple(e);
        onTouchStart?.(e);
    };

    const handleClick = (e: React.MouseEvent) => {
        onClick?.(e);
    };

    return (
        <div
            ref={containerRef}
            className={`relative overflow-hidden ${className}`}
            onMouseDown={handleMouseDown}
            onTouchStart={handleTouchStart}
            onClick={handleClick}
            {...props}
        >
            {children}
            
            {/* Ripple container */}
            <div className="absolute inset-0 pointer-events-none">
                {ripples.map((ripple) => (
                    <span
                        key={ripple.id}
                        className="absolute rounded-full animate-ripple"
                        style={{
                            left: ripple.x,
                            top: ripple.y,
                            width: ripple.size,
                            height: ripple.size,
                            backgroundColor: rippleColor,
                            opacity: rippleOpacity,
                            transform: 'scale(0)',
                            animation: `ripple ${rippleDuration}ms ease-out`,
                        }}
                    />
                ))}
            </div>
        </div>
    );
};

interface RippleButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    children: React.ReactNode;
    rippleColor?: string;
    rippleDuration?: number;
    variant?: 'primary' | 'secondary' | 'ghost' | 'outline';
}

const RippleButton: React.FC<RippleButtonProps> = ({
    children,
    className = '',
    rippleColor,
    rippleDuration = 600,
    variant = 'primary',
    disabled = false,
    onClick,
    ...props
}) => {
    const getVariantClasses = () => {
        switch (variant) {
            case 'primary':
                return 'bg-primary text-primary-foreground hover:bg-primary/90';
            case 'secondary':
                return 'bg-secondary text-secondary-foreground hover:bg-secondary/80';
            case 'ghost':
                return 'hover:bg-accent hover:text-accent-foreground';
            case 'outline':
                return 'border border-input bg-background hover:bg-accent hover:text-accent-foreground';
            default:
                return 'bg-primary text-primary-foreground hover:bg-primary/90';
        }
    };

    const getDefaultRippleColor = () => {
        if (rippleColor) return rippleColor;
        
        switch (variant) {
            case 'primary':
                return 'rgba(255, 255, 255, 0.3)';
            case 'secondary':
                return 'rgba(0, 0, 0, 0.1)';
            case 'ghost':
                return 'rgba(0, 0, 0, 0.1)';
            case 'outline':
                return 'rgba(0, 0, 0, 0.1)';
            default:
                return 'rgba(255, 255, 255, 0.3)';
        }
    };

    return (
        <RippleEffect
            className={`inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background px-4 py-2 ${getVariantClasses()} ${className}`}
            rippleColor={getDefaultRippleColor()}
            rippleDuration={rippleDuration}
            disabled={disabled}
            onClick={onClick}
            {...props}
        >
            <button className="w-full h-full flex items-center justify-center" disabled={disabled}>
                {children}
            </button>
        </RippleEffect>
    );
};

interface RippleCardProps extends React.HTMLAttributes<HTMLDivElement> {
    children: React.ReactNode;
    rippleColor?: string;
    rippleDuration?: number;
    hover?: boolean;
}

const RippleCard: React.FC<RippleCardProps> = ({
    children,
    className = '',
    rippleColor = 'rgba(0, 0, 0, 0.1)',
    rippleDuration = 600,
    hover = true,
    onClick,
    ...props
}) => {
    return (
        <RippleEffect
            className={`rounded-lg border bg-card text-card-foreground shadow-sm transition-colors ${
                hover ? 'hover:bg-accent/50 cursor-pointer' : ''
            } ${className}`}
            rippleColor={rippleColor}
            rippleDuration={rippleDuration}
            onClick={onClick}
            {...props}
        >
            {children}
        </RippleEffect>
    );
};

interface RippleIconButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    children: React.ReactNode;
    rippleColor?: string;
    rippleDuration?: number;
    size?: 'sm' | 'md' | 'lg';
}

const RippleIconButton: React.FC<RippleIconButtonProps> = ({
    children,
    className = '',
    rippleColor = 'rgba(0, 0, 0, 0.1)',
    rippleDuration = 400,
    size = 'md',
    disabled = false,
    onClick,
    ...props
}) => {
    const getSizeClasses = () => {
        switch (size) {
            case 'sm':
                return 'h-8 w-8';
            case 'md':
                return 'h-10 w-10';
            case 'lg':
                return 'h-12 w-12';
            default:
                return 'h-10 w-10';
        }
    };

    return (
        <RippleEffect
            className={`inline-flex items-center justify-center rounded-full transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ${getSizeClasses()} ${className}`}
            rippleColor={rippleColor}
            rippleDuration={rippleDuration}
            disabled={disabled}
            onClick={onClick}
            {...props}
        >
            <button className="w-full h-full flex items-center justify-center" disabled={disabled}>
                {children}
            </button>
        </RippleEffect>
    );
};

export { RippleEffect, RippleButton, RippleCard, RippleIconButton };
export default RippleEffect;
