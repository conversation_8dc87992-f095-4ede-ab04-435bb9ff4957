import React, { forwardRef } from 'react';
import { useHapticFeedback } from '../utils/hapticFeedback';

type HapticType = 'light' | 'medium' | 'heavy' | 'success' | 'error' | 'warning' | 'selection' | 'navigation';

interface HapticButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    hapticType?: HapticType;
    hapticOnPress?: boolean;
    hapticOnRelease?: boolean;
    children: React.ReactNode;
}

const HapticButton = forwardRef<HTMLButtonElement, HapticButtonProps>(({
    hapticType = 'light',
    hapticOnPress = true,
    hapticOnRelease = false,
    children,
    onMouseDown,
    onMouseUp,
    onTouchStart,
    onTouchEnd,
    onClick,
    className = '',
    ...props
}, ref) => {
    const haptic = useHapticFeedback();

    const triggerHaptic = () => {
        if (!haptic.isSupported || !haptic.isEnabled) return;

        switch (hapticType) {
            case 'light':
                haptic.light();
                break;
            case 'medium':
                haptic.medium();
                break;
            case 'heavy':
                haptic.heavy();
                break;
            case 'success':
                haptic.success();
                break;
            case 'error':
                haptic.error();
                break;
            case 'warning':
                haptic.warning();
                break;
            case 'selection':
                haptic.selection();
                break;
            case 'navigation':
                haptic.navigation();
                break;
        }
    };

    const handleMouseDown = (e: React.MouseEvent<HTMLButtonElement>) => {
        if (hapticOnPress) {
            triggerHaptic();
        }
        onMouseDown?.(e);
    };

    const handleMouseUp = (e: React.MouseEvent<HTMLButtonElement>) => {
        if (hapticOnRelease) {
            triggerHaptic();
        }
        onMouseUp?.(e);
    };

    const handleTouchStart = (e: React.TouchEvent<HTMLButtonElement>) => {
        if (hapticOnPress) {
            triggerHaptic();
        }
        onTouchStart?.(e);
    };

    const handleTouchEnd = (e: React.TouchEvent<HTMLButtonElement>) => {
        if (hapticOnRelease) {
            triggerHaptic();
        }
        onTouchEnd?.(e);
    };

    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
        // Only trigger haptic on click if neither press nor release is enabled
        if (!hapticOnPress && !hapticOnRelease) {
            triggerHaptic();
        }
        onClick?.(e);
    };

    return (
        <button
            ref={ref}
            className={`transition-transform active:scale-95 ${className}`}
            onMouseDown={handleMouseDown}
            onMouseUp={handleMouseUp}
            onTouchStart={handleTouchStart}
            onTouchEnd={handleTouchEnd}
            onClick={handleClick}
            {...props}
        >
            {children}
        </button>
    );
});

HapticButton.displayName = 'HapticButton';

interface HapticLinkProps extends React.AnchorHTMLAttributes<HTMLAnchorElement> {
    hapticType?: HapticType;
    children: React.ReactNode;
}

const HapticLink = forwardRef<HTMLAnchorElement, HapticLinkProps>(({
    hapticType = 'selection',
    children,
    onMouseDown,
    onTouchStart,
    className = '',
    ...props
}, ref) => {
    const haptic = useHapticFeedback();

    const triggerHaptic = () => {
        if (!haptic.isSupported || !haptic.isEnabled) return;

        switch (hapticType) {
            case 'light':
                haptic.light();
                break;
            case 'medium':
                haptic.medium();
                break;
            case 'heavy':
                haptic.heavy();
                break;
            case 'success':
                haptic.success();
                break;
            case 'error':
                haptic.error();
                break;
            case 'warning':
                haptic.warning();
                break;
            case 'selection':
                haptic.selection();
                break;
            case 'navigation':
                haptic.navigation();
                break;
        }
    };

    const handleMouseDown = (e: React.MouseEvent<HTMLAnchorElement>) => {
        triggerHaptic();
        onMouseDown?.(e);
    };

    const handleTouchStart = (e: React.TouchEvent<HTMLAnchorElement>) => {
        triggerHaptic();
        onTouchStart?.(e);
    };

    return (
        <a
            ref={ref}
            className={`transition-transform active:scale-95 ${className}`}
            onMouseDown={handleMouseDown}
            onTouchStart={handleTouchStart}
            {...props}
        >
            {children}
        </a>
    );
});

HapticLink.displayName = 'HapticLink';

interface HapticCardProps extends React.HTMLAttributes<HTMLDivElement> {
    hapticType?: HapticType;
    children: React.ReactNode;
}

const HapticCard = forwardRef<HTMLDivElement, HapticCardProps>(({
    hapticType = 'selection',
    children,
    onMouseDown,
    onTouchStart,
    className = '',
    ...props
}, ref) => {
    const haptic = useHapticFeedback();

    const triggerHaptic = () => {
        if (!haptic.isSupported || !haptic.isEnabled) return;

        switch (hapticType) {
            case 'selection':
                haptic.selection();
                break;
            case 'light':
                haptic.light();
                break;
            case 'medium':
                haptic.medium();
                break;
            default:
                haptic.selection();
        }
    };

    const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
        triggerHaptic();
        onMouseDown?.(e);
    };

    const handleTouchStart = (e: React.TouchEvent<HTMLDivElement>) => {
        triggerHaptic();
        onTouchStart?.(e);
    };

    return (
        <div
            ref={ref}
            className={`transition-transform active:scale-98 ${className}`}
            onMouseDown={handleMouseDown}
            onTouchStart={handleTouchStart}
            {...props}
        >
            {children}
        </div>
    );
});

HapticCard.displayName = 'HapticCard';

export { HapticButton, HapticLink, HapticCard };
export default HapticButton;
