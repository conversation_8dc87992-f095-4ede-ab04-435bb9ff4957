import React from 'react';

interface MobileLayoutProps {
    children: React.ReactNode;
    variant?: 'default' | 'edge-to-edge' | 'full-width' | 'immersive';
    className?: string;
}

const MobileLayout: React.FC<MobileLayoutProps> = ({ 
    children, 
    variant = 'default', 
    className = '' 
}) => {
    const getLayoutClasses = () => {
        switch (variant) {
            case 'edge-to-edge':
                return 'mobile-edge-to-edge md:mx-0 md:w-auto';
            case 'full-width':
                return 'mobile-full-width md:mx-auto md:w-auto md:max-w-screen-xl';
            case 'immersive':
                return 'mobile-immersive md:min-h-auto md:mx-auto md:w-auto md:max-w-screen-xl md:p-4';
            default:
                return 'mobile-container-fluid md:mx-auto md:max-w-screen-xl md:px-4';
        }
    };

    return (
        <div className={`${getLayoutClasses()} ${className}`}>
            {children}
        </div>
    );
};

interface MobileImageProps {
    src: string;
    alt: string;
    className?: string;
    aspectRatio?: 'square' | 'video' | 'wide' | 'tall';
    overlay?: React.ReactNode;
}

const MobileImage: React.FC<MobileImageProps> = ({ 
    src, 
    alt, 
    className = '', 
    aspectRatio = 'video',
    overlay 
}) => {
    const getAspectRatio = () => {
        switch (aspectRatio) {
            case 'square':
                return 'aspect-square';
            case 'video':
                return 'aspect-video';
            case 'wide':
                return 'aspect-[21/9]';
            case 'tall':
                return 'aspect-[4/5]';
            default:
                return 'aspect-video';
        }
    };

    return (
        <div className={`mobile-edge-to-edge md:mx-0 md:w-auto relative overflow-hidden ${className}`}>
            <div className={`w-full ${getAspectRatio()} bg-muted`}>
                <img
                    src={src}
                    alt={alt}
                    className="w-full h-full object-cover"
                    loading="lazy"
                    decoding="async"
                />
                {overlay && (
                    <div className="mobile-content-overlay md:relative md:bg-none md:text-inherit md:p-0">
                        {overlay}
                    </div>
                )}
            </div>
        </div>
    );
};

interface MobileSectionProps {
    children: React.ReactNode;
    background?: 'default' | 'muted' | 'accent' | 'primary';
    fullWidth?: boolean;
    className?: string;
}

const MobileSection: React.FC<MobileSectionProps> = ({ 
    children, 
    background = 'default',
    fullWidth = false,
    className = '' 
}) => {
    const getBackgroundClasses = () => {
        switch (background) {
            case 'muted':
                return 'bg-muted';
            case 'accent':
                return 'bg-accent';
            case 'primary':
                return 'bg-primary text-primary-foreground';
            default:
                return 'bg-background';
        }
    };

    const layoutClasses = fullWidth 
        ? 'mobile-section-full md:mx-auto md:w-auto md:max-w-screen-xl md:px-4'
        : 'mobile-container-fluid md:mx-auto md:max-w-screen-xl md:px-4';

    return (
        <section className={`${layoutClasses} ${getBackgroundClasses()} ${className}`}>
            {children}
        </section>
    );
};

interface MobileCardProps {
    children: React.ReactNode;
    image?: {
        src: string;
        alt: string;
        aspectRatio?: 'square' | 'video' | 'wide' | 'tall';
    };
    edgeToEdgeImage?: boolean;
    className?: string;
}

const MobileCard: React.FC<MobileCardProps> = ({ 
    children, 
    image,
    edgeToEdgeImage = true,
    className = '' 
}) => {
    return (
        <article className={`mobile-card-edge-image md:border md:border-border md:rounded-lg md:overflow-hidden md:bg-card md:shadow-sm ${className}`}>
            {image && (
                <div className={edgeToEdgeImage ? 'mobile-image md:m-0 md:w-auto' : ''}>
                    <MobileImage 
                        src={image.src} 
                        alt={image.alt} 
                        aspectRatio={image.aspectRatio}
                        className={!edgeToEdgeImage ? 'mx-0 w-auto' : ''}
                    />
                </div>
            )}
            <div className="mobile-container-fluid md:p-4">
                {children}
            </div>
        </article>
    );
};

interface MobileGridProps {
    children: React.ReactNode;
    columns?: 1 | 2;
    gap?: 'sm' | 'md' | 'lg';
    className?: string;
}

const MobileGrid: React.FC<MobileGridProps> = ({ 
    children, 
    columns = 1,
    gap = 'md',
    className = '' 
}) => {
    const getGridClasses = () => {
        const gapClasses = {
            sm: 'gap-2',
            md: 'gap-4',
            lg: 'gap-6'
        };

        return `grid grid-cols-${columns} md:grid-cols-2 lg:grid-cols-3 ${gapClasses[gap]}`;
    };

    return (
        <div className={`${getGridClasses()} ${className}`}>
            {children}
        </div>
    );
};

export { MobileLayout, MobileImage, MobileSection, MobileCard, MobileGrid };
export default MobileLayout;
