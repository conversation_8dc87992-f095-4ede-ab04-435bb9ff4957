
import React, { useState, useContext, useEffect, useRef } from 'react';
import { NavLink, Link } from 'react-router-dom';
import { useTheme } from '../context/ThemeContext';
import { BlogContext } from '../context/SupabaseBlogContext';
import { MenuIcon, SearchIcon, GlobeAltIcon } from './icons';
import { ThemeToggle } from '../src/components/ThemeSelector';
import { HapticButton, HapticLink } from './HapticButton';
import { RippleIconButton } from './RippleEffect';
import { useHapticFeedback } from '../utils/hapticFeedback';

const Header: React.FC = () => {
    const { isDarkMode } = useTheme();
    const context = useContext(BlogContext);
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
    const [isCategoriesOpen, setIsCategoriesOpen] = useState(false);
    const [isScrolled, setIsScrolled] = useState(false);
    const [isVisible, setIsVisible] = useState(true);
    const [lastScrollY, setLastScrollY] = useState(0);
    const dropdownRef = useRef<HTMLLIElement>(null);
    const haptic = useHapticFeedback();

    // Handle scroll behavior for mobile sticky header
    useEffect(() => {
        const handleScroll = () => {
            const currentScrollY = window.scrollY;

            // Show/hide header based on scroll direction (mobile only)
            if (window.innerWidth < 768) {
                if (currentScrollY > lastScrollY && currentScrollY > 100) {
                    setIsVisible(false);
                } else {
                    setIsVisible(true);
                }
            } else {
                setIsVisible(true);
            }

            // Add backdrop blur when scrolled
            setIsScrolled(currentScrollY > 20);
            setLastScrollY(currentScrollY);
        };

        // Use passive listener for better performance
        window.addEventListener('scroll', handleScroll, { passive: true });
        return () => window.removeEventListener('scroll', handleScroll);
    }, [lastScrollY]);

    // Close dropdown when clicking outside - optimized with passive listener
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsCategoriesOpen(false);
            }
        };

        // Use passive listener for better performance
        document.addEventListener('mousedown', handleClickOutside, { passive: true });
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    const NavItem: React.FC<{ to: string; children: React.ReactNode; mobile?: boolean }> = ({ to, children, mobile = false }) => (
        <li>
            <HapticLink
                href={to}
                hapticType="navigation"
                onClick={(e) => {
                    e.preventDefault();
                    if (mobile) {
                        setIsMobileMenuOpen(false);
                        haptic.navigation();
                    }
                    // Handle navigation programmatically if needed
                    window.location.href = to;
                }}
                className={`flex items-center gap-2 text-sm font-medium text-dark-text dark:text-light-text hover:text-primary dark:hover:text-primary transition-all duration-200 ${mobile ? 'mobile-nav-item rounded-lg hover:bg-accent/50 active:bg-accent/70' : ''}`}
            >
                <div className="w-2 h-2 rounded-full border border-primary dark:border-primary"></div>
                {children}
            </HapticLink>
        </li>
    );

    return (
        <>
            <header className={`bg-background transition-all duration-300 relative ${
                isScrolled ? 'md:shadow-sm' : ''
            }`}>
                {/* Desktop Header - Always visible */}
                <div className="hidden md:block">
                    <div className="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8">
                        {/* Logo Section */}
                        <div className="text-center py-6 sm:py-8">
                            <div className="inline-block relative mb-2 sm:mb-4">

                            </div>
                            <Link to="/" className="block">
                                <h1 className="text-3xl sm:text-4xl lg:text-5xl font-serif tracking-widest text-foreground hover:text-primary transition-colors">
                                    behindyourbrain
                                </h1>
                                <p className="text-xs tracking-[0.2em] text-muted-foreground mt-1">
                                    CREATIVE MAGAZINE
                                </p>
                            </Link>
                        </div>

                        {/* Navigation Section */}
                        <div className="flex justify-between items-center border-t border-b border-border py-4">
                            {/* Mobile Menu Button */}
                            <button
                                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                                className="w-8 h-8 flex items-center justify-center text-foreground hover:text-primary transition-colors"
                                aria-label="Toggle mobile menu"
                            >
                                <MenuIcon className="w-6 h-6" />
                            </button>

                            {/* Desktop Navigation */}
                            <nav className="flex flex-1 justify-center">
                            <ul className="flex items-center space-x-6 lg:space-x-8">
                                <NavItem to="/">Home</NavItem>
                                
                                {/* Categories Dropdown */}
                                <li className="relative" ref={dropdownRef}>
                                    <button
                                        onClick={() => setIsCategoriesOpen(!isCategoriesOpen)}
                                        className="flex items-center gap-2 text-sm font-medium text-dark-text dark:text-light-text hover:text-primary dark:hover:text-primary transition-colors"
                                    >
                                        <div className="w-2 h-2 rounded-full border border-primary dark:border-primary"></div>
                                        Categories
                                        <svg className={`w-4 h-4 transition-transform ${isCategoriesOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                        </svg>
                                    </button>
                                    
                                    {isCategoriesOpen && context && (
                                        <div className="absolute top-full left-0 mt-2 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50">
                                            <div className="py-2">
                                                {context.categories.map((category) => (
                                                    <Link
                                                        key={category.id}
                                                        to={`/category/${category.slug}`}
                                                        onClick={() => setIsCategoriesOpen(false)}
                                                        className="block px-4 py-2 text-sm text-dark-text dark:text-light-text hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-primary dark:hover:text-primary transition-colors"
                                                    >
                                                        {category.name}
                                                    </Link>
                                                ))}
                                            </div>
                                        </div>
                                    )}
                                </li>
                                
                                <NavItem to="/mobile-demo">Mobile Demo</NavItem>
                                <NavItem to="/admin">Contact</NavItem>
                            </ul>
                        </nav>
                        
                            {/* Action Buttons */}
                            <div className="flex items-center space-x-2 sm:space-x-4">
                                <ThemeToggle />

                                <button className="hidden sm:flex w-10 h-10 border rounded-full border-border items-center justify-center hover:bg-accent hover:text-accent-foreground transition-colors" aria-label="Search">
                                    <SearchIcon className="w-4 h-4 text-foreground" />
                                </button>

                                <div className="hidden sm:flex items-center gap-2 text-sm">
                                    <button className="w-10 h-10 border rounded-full border-border flex items-center justify-center hover:bg-accent hover:text-accent-foreground transition-colors" aria-label="Language">
                                        <GlobeAltIcon className="w-5 h-5 text-foreground" />
                                    </button>
                                    <span className="text-foreground text-xs">EN</span>
                                </div>

                                <Link
                                    to="/login"
                                    className="px-4 sm:px-6 py-3 bg-primary dark:bg-primary-dark rounded-md text-white text-xs sm:text-sm font-medium hover:bg-primary-dark dark:hover:bg-primary transition-colors min-h-[44px] flex items-center"
                                >
                                    Login
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Mobile Sticky Header */}
                <div className={`md:hidden fixed top-0 left-0 right-0 z-40 transition-all duration-300 ${
                    isVisible ? 'translate-y-0' : '-translate-y-full'
                } ${
                    isScrolled
                        ? 'bg-background/95 backdrop-blur-md border-b border-border/50 shadow-sm'
                        : 'bg-background'
                }`}>
                    <div className="px-4 py-3">
                        <div className="flex items-center justify-between">
                            {/* Mobile Logo */}
                            <Link to="/" className="flex items-center">
                                <h1 className="text-lg font-serif tracking-wider text-foreground">
                                    behindyourbrain
                                </h1>
                            </Link>

                            {/* Mobile Actions */}
                            <div className="flex items-center space-x-3">
                                <ThemeToggle />
                                <RippleIconButton
                                    onClick={() => {
                                        setIsMobileMenuOpen(!isMobileMenuOpen);
                                        haptic.medium();
                                    }}
                                    className="text-foreground hover:text-primary transition-colors hover:bg-accent/50"
                                    rippleColor="rgba(var(--primary), 0.2)"
                                    size="lg"
                                    aria-label="Toggle mobile menu"
                                >
                                    <MenuIcon className="w-6 h-6" />
                                </RippleIconButton>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Mobile Navigation Menu */}
                <div className={`md:hidden fixed top-16 left-0 right-0 bg-background/95 backdrop-blur-md border-b border-border/50 shadow-lg z-30 max-h-[calc(100vh-4rem)] overflow-y-auto transition-all duration-300 ease-in-out ${
                    isMobileMenuOpen
                        ? 'translate-y-0 opacity-100'
                        : '-translate-y-full opacity-0 pointer-events-none'
                }`}>
                        <nav className="px-4">
                            <ul className="py-6 space-y-1">
                                <div className="mobile-menu-item">
                                    <NavItem to="/" mobile>Home</NavItem>
                                </div>
                                
                                {/* Mobile Categories */}
                                <div className="mobile-menu-item">
                                    <li>
                                    <button
                                        onClick={() => setIsCategoriesOpen(!isCategoriesOpen)}
                                        className="mobile-nav-item justify-between w-full text-sm font-medium text-dark-text dark:text-light-text hover:text-primary dark:hover:text-primary transition-all duration-200 rounded-lg hover:bg-accent/50 active:bg-accent/70 active:scale-95"
                                    >
                                        <span className="flex items-center gap-2">
                                            <div className="w-2 h-2 rounded-full border border-primary dark:border-primary"></div>
                                            Categories
                                        </span>
                                        <svg className={`w-4 h-4 transition-transform duration-200 ${isCategoriesOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                        </svg>
                                    </button>

                                    <div className={`overflow-hidden transition-all duration-300 ease-in-out ${
                                        isCategoriesOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                                    }`}>
                                        {context && (
                                            <div className="pl-6 py-2 space-y-1">
                                                {context.categories.map((category, index) => (
                                                    <Link
                                                        key={category.id}
                                                        to={`/category/${category.slug}`}
                                                        onClick={() => {
                                                            setIsCategoriesOpen(false);
                                                            setIsMobileMenuOpen(false);
                                                        }}
                                                        className="mobile-link text-sm text-gray-600 dark:text-gray-400 hover:text-primary dark:hover:text-primary transition-all duration-200 hover:bg-accent/30 active:bg-accent/50 active:scale-95"
                                                        style={{
                                                            animationDelay: `${index * 50}ms`,
                                                            animation: isCategoriesOpen ? 'slideInLeft 0.3s ease-out forwards' : 'none'
                                                        }}
                                                    >
                                                        {category.name}
                                                    </Link>
                                                ))}
                                            </div>
                                        )}
                                    </div>
                                    </li>
                                </div>

                                <div className="mobile-menu-item">
                                    <NavItem to="/mobile-demo" mobile>Mobile Demo</NavItem>
                                </div>
                                <div className="mobile-menu-item">
                                    <NavItem to="/admin" mobile>Contact</NavItem>
                                </div>
                            </ul>
                            <div className="py-4 border-t border-slate-200 dark:border-slate-700 flex items-center justify-center space-x-4">
                                <button className="mobile-icon-button border border-slate-400 dark:border-slate-500 hover:bg-slate-100 dark:hover:bg-medium-dark transition-colors" aria-label="Search">
                                    <SearchIcon className="w-4 h-4 text-dark-text dark:text-light-text" />
                                </button>
                                <div className="flex items-center gap-2 text-sm">
                                    <button className="mobile-icon-button border border-slate-400 dark:border-slate-500 hover:bg-slate-100 dark:hover:bg-medium-dark transition-colors" aria-label="Language">
                                        <GlobeAltIcon className="w-5 h-5 text-dark-text dark:text-light-text" />
                                    </button>
                                    <span className="text-dark-text dark:text-light-text text-xs">EN</span>
                                </div>
                            </div>
                        </nav>
                    </div>
                )}
            </header>
        </>
    );
};

export default Header;
