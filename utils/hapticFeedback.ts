// Haptic Feedback Utility for Web Vibration API
// Provides native app-like tactile feedback for touch interactions

interface HapticPattern {
    pattern: number | number[];
    description: string;
}

class HapticFeedback {
    private isSupported: boolean;
    private isEnabled: boolean;

    constructor() {
        this.isSupported = 'vibrate' in navigator;
        this.isEnabled = this.isSupported && this.getUserPreference();
    }

    /**
     * Check if haptic feedback is supported by the device
     */
    public isHapticSupported(): boolean {
        return this.isSupported;
    }

    /**
     * Check if haptic feedback is currently enabled
     */
    public isHapticEnabled(): boolean {
        return this.isEnabled;
    }

    /**
     * Enable or disable haptic feedback
     */
    public setEnabled(enabled: boolean): void {
        this.isEnabled = enabled && this.isSupported;
        this.saveUserPreference(enabled);
    }

    /**
     * Get user preference from localStorage
     */
    private getUserPreference(): boolean {
        try {
            const preference = localStorage.getItem('haptic-feedback-enabled');
            return preference !== null ? JSON.parse(preference) : true; // Default to enabled
        } catch {
            return true;
        }
    }

    /**
     * Save user preference to localStorage
     */
    private saveUserPreference(enabled: boolean): void {
        try {
            localStorage.setItem('haptic-feedback-enabled', JSON.stringify(enabled));
        } catch {
            // Silently fail if localStorage is not available
        }
    }

    /**
     * Trigger haptic feedback with a specific pattern
     */
    private vibrate(pattern: number | number[]): void {
        if (!this.isEnabled) return;

        try {
            navigator.vibrate(pattern);
        } catch (error) {
            console.warn('Haptic feedback failed:', error);
        }
    }

    // Predefined haptic patterns for different interactions

    /**
     * Light tap feedback for button presses and small interactions
     */
    public light(): void {
        this.vibrate(10);
    }

    /**
     * Medium feedback for important actions
     */
    public medium(): void {
        this.vibrate(20);
    }

    /**
     * Heavy feedback for significant actions
     */
    public heavy(): void {
        this.vibrate(40);
    }

    /**
     * Success feedback pattern
     */
    public success(): void {
        this.vibrate([10, 50, 10]);
    }

    /**
     * Error feedback pattern
     */
    public error(): void {
        this.vibrate([50, 100, 50, 100, 50]);
    }

    /**
     * Warning feedback pattern
     */
    public warning(): void {
        this.vibrate([30, 50, 30]);
    }

    /**
     * Selection feedback for list items, cards, etc.
     */
    public selection(): void {
        this.vibrate(15);
    }

    /**
     * Navigation feedback for menu transitions
     */
    public navigation(): void {
        this.vibrate([5, 30, 5]);
    }

    /**
     * Swipe feedback for gesture interactions
     */
    public swipe(): void {
        this.vibrate([8, 20, 8]);
    }

    /**
     * Long press feedback
     */
    public longPress(): void {
        this.vibrate([20, 50, 20]);
    }

    /**
     * Notification feedback
     */
    public notification(): void {
        this.vibrate([10, 100, 10, 100, 10]);
    }

    /**
     * Custom pattern feedback
     */
    public custom(pattern: number | number[]): void {
        this.vibrate(pattern);
    }

    /**
     * Get all available haptic patterns
     */
    public getPatterns(): Record<string, HapticPattern> {
        return {
            light: { pattern: 10, description: 'Light tap for buttons' },
            medium: { pattern: 20, description: 'Medium feedback for actions' },
            heavy: { pattern: 40, description: 'Heavy feedback for significant actions' },
            success: { pattern: [10, 50, 10], description: 'Success confirmation' },
            error: { pattern: [50, 100, 50, 100, 50], description: 'Error indication' },
            warning: { pattern: [30, 50, 30], description: 'Warning alert' },
            selection: { pattern: 15, description: 'Item selection' },
            navigation: { pattern: [5, 30, 5], description: 'Navigation transition' },
            swipe: { pattern: [8, 20, 8], description: 'Swipe gesture' },
            longPress: { pattern: [20, 50, 20], description: 'Long press action' },
            notification: { pattern: [10, 100, 10, 100, 10], description: 'Notification alert' }
        };
    }
}

// Create singleton instance
const hapticFeedback = new HapticFeedback();

// React hook for haptic feedback
export const useHapticFeedback = () => {
    return {
        isSupported: hapticFeedback.isHapticSupported(),
        isEnabled: hapticFeedback.isHapticEnabled(),
        setEnabled: (enabled: boolean) => hapticFeedback.setEnabled(enabled),
        light: () => hapticFeedback.light(),
        medium: () => hapticFeedback.medium(),
        heavy: () => hapticFeedback.heavy(),
        success: () => hapticFeedback.success(),
        error: () => hapticFeedback.error(),
        warning: () => hapticFeedback.warning(),
        selection: () => hapticFeedback.selection(),
        navigation: () => hapticFeedback.navigation(),
        swipe: () => hapticFeedback.swipe(),
        longPress: () => hapticFeedback.longPress(),
        notification: () => hapticFeedback.notification(),
        custom: (pattern: number | number[]) => hapticFeedback.custom(pattern),
        getPatterns: () => hapticFeedback.getPatterns()
    };
};

export default hapticFeedback;
